<?php

namespace App\Jobs;

use App\Models\ScrapingJob;
use App\Services\ScrapingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ScrapeBusinessesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300; // 5 minutes

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return int
     */
    public function backoff()
    {
        return [60, 180, 600]; // 1 minute, 3 minutes, 10 minutes
    }

    /**
     * The scraping job instance.
     *
     * @var ScrapingJob
     */
    protected $scrapingJob;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(ScrapingJob $scrapingJob)
    {
        $this->scrapingJob = $scrapingJob;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(ScrapingService $scrapingService)
    {
        // Refresh the model to get the latest status
        $this->scrapingJob->refresh();

        if ($this->scrapingJob->status !== \App\Enums\ScrapingJobStatusEnum::PENDING) {
            Log::warning('ScrapeBusinessesJob called for a job that is not pending.', [
                'scraping_job_id' => $this->scrapingJob->id,
                'status' => $this->scrapingJob->status,
                'attempt' => $this->attempts()
            ]);
            return;
        }

        try {
            $this->scrapingJob->update([
                'status' => \App\Enums\ScrapingJobStatusEnum::IN_PROGRESS,
                'message' => 'Scraping in progress... (Attempt ' . $this->attempts() . '/' . $this->tries . ')'
            ]);

            $scrapingService->scrape($this->scrapingJob);

        } catch (\Exception $e) {
            $isLastAttempt = $this->attempts() >= $this->tries;

            Log::error('ScrapeBusinessesJob failed.', [
                'scraping_job_id' => $this->scrapingJob->id,
                'attempt' => $this->attempts(),
                'max_attempts' => $this->tries,
                'is_last_attempt' => $isLastAttempt,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if ($isLastAttempt) {
                // Final failure - mark as failed but don't reject campaign yet
                // The continuous scraping logic will handle expanding search area
                $this->scrapingJob->update([
                    'status' => \App\Enums\ScrapingJobStatusEnum::FAILED,
                    'message' => 'Scraping failed after ' . $this->tries . ' attempts. Last error: ' . $e->getMessage()
                ]);

                // Don't automatically reject the campaign - let the ScrapingService handle continuous scraping
                $campaign = $this->scrapingJob->jobNotificationCampaign;
                Log::info('Scraping job failed, but campaign will continue with expanded search area', [
                    'campaign_id' => $campaign->id,
                    'scraping_job_id' => $this->scrapingJob->id,
                    'current_radius' => $campaign->search_radius
                ]);

                // Trigger continuous scraping through ScrapingService
                $scrapingService = app(\App\Services\ScrapingService::class);
                $scrapingService->triggerContinuousScraping($campaign, $this->scrapingJob);
            } else {
                // Will retry - update status back to pending
                $this->scrapingJob->update([
                    'status' => \App\Enums\ScrapingJobStatusEnum::PENDING,
                    'message' => 'Scraping failed (Attempt ' . $this->attempts() . '/' . $this->tries . '). Will retry. Error: ' . $e->getMessage()
                ]);
            }

            // Re-throw exception to let Laravel handle the job failure (retry, etc.)
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('ScrapeBusinessesJob permanently failed', [
            'scraping_job_id' => $this->scrapingJob->id,
            'attempts_made' => $this->attempts(),
            'error' => $exception->getMessage(),
        ]);

        // Ensure the scraping job is marked as failed
        $this->scrapingJob->update([
            'status' => \App\Enums\ScrapingJobStatusEnum::FAILED,
            'message' => 'Scraping permanently failed: ' . $exception->getMessage()
        ]);

        // Don't automatically reject the campaign - trigger continuous scraping instead
        $campaign = $this->scrapingJob->jobNotificationCampaign;

        Log::info('Scraping job permanently failed, triggering continuous scraping with expanded area', [
            'campaign_id' => $campaign->id,
            'scraping_job_id' => $this->scrapingJob->id,
            'current_radius' => $campaign->search_radius
        ]);

        try {
            // Trigger continuous scraping through ScrapingService
            $scrapingService = app(\App\Services\ScrapingService::class);
            $scrapingService->triggerContinuousScraping($campaign, $this->scrapingJob);
        } catch (\Exception $e) {
            // If continuous scraping also fails, then reject the campaign
            Log::error('Failed to trigger continuous scraping, rejecting campaign', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage()
            ]);

            $campaign->update([
                'status' => \App\Enums\JobNotificationStatusEnum::REJECTED,
                'rejection_reason' => 'Scraping permanently failed and continuous scraping could not be triggered: ' . $exception->getMessage()
            ]);
        }
    }
}