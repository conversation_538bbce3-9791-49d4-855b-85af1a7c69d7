<?php

namespace App\Services;

use App\Models\Business;
use App\Models\ScrapingJob;
use App\Services\JobNotificationService;
use App\Services\BusinessDataCompletenessService;
use App\Jobs\EnhanceBusinessDataJob;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;

class ScrapingService
{
    protected $jobNotificationService;
    protected $completenessService;

    public function __construct(JobNotificationService $jobNotificationService, BusinessDataCompletenessService $completenessService)
    {
        $this->jobNotificationService = $jobNotificationService;
        $this->completenessService = $completenessService;
    }

    /**
     * Scrape businesses for a given scraping job.
     *
     * @param ScrapingJob $scrapingJob
     * @return void
     */
    public function scrape(ScrapingJob $scrapingJob): void
    {
        Log::info('Starting to scrape businesses for scraping job', ['scraping_job_id' => $scrapingJob->id]);

        // Check if this is for enhancing existing businesses or finding new ones
        $campaign = $scrapingJob->jobNotificationCampaign;
        $existingBusinesses = $this->findExistingBusinessesInArea($campaign->job_zip_code, $campaign->job_category);

        if ($existingBusinesses->isNotEmpty()) {
            Log::info('Found existing businesses in area, checking for data completeness', [
                'scraping_job_id' => $scrapingJob->id,
                'existing_businesses_count' => $existingBusinesses->count(),
            ]);

            // Enhance existing businesses with incomplete data
            $this->enhanceExistingBusinesses($scrapingJob, $existingBusinesses);
        } else {
            Log::info('No existing businesses found, scraping new businesses', [
                'scraping_job_id' => $scrapingJob->id,
            ]);

            // Perform real scraping using Node.js scraper
            $this->performRealScraping($scrapingJob);
        }

        // Once scraping is done and businesses are added, we need to
        // re-trigger the notification process for the original campaign.
        $this->processCampaignAfterScraping($scrapingJob);
    }

    /**
     * Perform real scraping using Node.js scraper and create businesses from scraped data.
     *
     * @param ScrapingJob $scrapingJob
     */
    protected function performRealScraping(ScrapingJob $scrapingJob): void
    {
        $locationQuery = $scrapingJob->location_query;
        $categoryQuery = $scrapingJob->category_query;
        $discoveredCount = 0;

        Log::info('Starting real scraping process', [
            'scraping_job_id' => $scrapingJob->id,
            'location_query' => $locationQuery,
            'category_query' => $categoryQuery,
        ]);

        try {
            // Call the Node.js scraper
            $scrapedData = $this->callNodeJsScraper($locationQuery, $categoryQuery);

            if (empty($scrapedData)) {
                Log::warning('No businesses found by Node.js scraper', [
                    'scraping_job_id' => $scrapingJob->id,
                    'location_query' => $locationQuery,
                    'category_query' => $categoryQuery,
                ]);

                $scrapingJob->update([
                    'discovered_businesses_count' => 0,
                    'status' => \App\Enums\ScrapingJobStatusEnum::COMPLETED,
                    'message' => "No businesses found for {$categoryQuery} in {$locationQuery}."
                ]);
                return;
            }

            // Process each scraped business
            foreach ($scrapedData as $businessData) {
                try {
                    // Validate and clean the scraped data
                    $cleanedData = $this->validateAndCleanScrapedData($businessData, $categoryQuery, $locationQuery);

                    if ($cleanedData) {
                        // Check if business already exists to avoid duplicates
                        $existingBusiness = $this->findExistingBusiness($cleanedData);

                        if (!$existingBusiness) {
                            $business = Business::create($cleanedData);
                            $discoveredCount++;

                            Log::info('Created business from scraped data', [
                                'business_id' => $business->id,
                                'business_name' => $business->name,
                                'scraping_job_id' => $scrapingJob->id,
                            ]);
                        } else {
                            Log::info('Business already exists, skipping', [
                                'existing_business_id' => $existingBusiness->id,
                                'business_name' => $existingBusiness->name,
                                'scraping_job_id' => $scrapingJob->id,
                            ]);
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to create business from scraped data', [
                        'scraping_job_id' => $scrapingJob->id,
                        'business_data' => $businessData,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('Real scraping completed successfully', [
                'scraping_job_id' => $scrapingJob->id,
                'location_query' => $locationQuery,
                'category_query' => $categoryQuery,
                'discovered_businesses_count' => $discoveredCount,
                'total_scraped' => count($scrapedData)
            ]);

            $scrapingJob->update([
                'discovered_businesses_count' => $discoveredCount,
                'status' => \App\Enums\ScrapingJobStatusEnum::COMPLETED,
                'message' => "Real scraping completed successfully. Found {$discoveredCount} businesses from " . count($scrapedData) . " scraped entries."
            ]);

        } catch (\Exception $e) {
            Log::error('Real scraping failed', [
                'scraping_job_id' => $scrapingJob->id,
                'location_query' => $locationQuery,
                'category_query' => $categoryQuery,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            $scrapingJob->update([
                'discovered_businesses_count' => 0,
                'status' => \App\Enums\ScrapingJobStatusEnum::FAILED,
                'message' => "Real scraping failed: " . $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Find existing businesses in the area that might need data enhancement
     *
     * @param string $zipCode
     * @param string|null $category
     * @return Collection
     */
    protected function findExistingBusinessesInArea(string $zipCode, ?string $category = null): Collection
    {
        // Use BusinessDiscoveryService to find businesses, but don't filter by email
        // since we want to find businesses that might be missing email or other data
        $businessDiscoveryService = app(BusinessDiscoveryService::class);

        // Get all businesses in the area, not just those with complete data
        $query = Business::query();

        // Get zip codes within radius
        $zipCodeService = app(\App\Services\ZipCode\ZipCode::class);
        $radius = config('job_notification.default_radius', 30);
        $zipCodes = $zipCodeService->getZipCodesInRadiusRaw($zipCode, $radius);

        if (empty($zipCodes)) {
            $query->where('zip_code', $zipCode);
        } else {
            $query->whereIn('zip_code', $zipCodes);
        }

        // Filter by category if provided
        if (!is_null($category)) {
            $query->where('category', $category);
        }

        return $query->get();
    }

    /**
     * Enhance existing businesses with incomplete data
     *
     * @param ScrapingJob $scrapingJob
     * @param Collection $businesses
     * @return void
     */
    protected function enhanceExistingBusinesses(ScrapingJob $scrapingJob, Collection $businesses): void
    {
        $incompleteBusinesses = $this->completenessService->filterIncompleteBusinesses($businesses);

        if ($incompleteBusinesses->isEmpty()) {
            Log::info('All existing businesses have complete data', [
                'scraping_job_id' => $scrapingJob->id,
                'total_businesses' => $businesses->count(),
            ]);

            $scrapingJob->update([
                'discovered_businesses_count' => 0,
                'status' => \App\Enums\ScrapingJobStatusEnum::COMPLETED,
                'message' => 'All existing businesses already have complete data.'
            ]);

            return;
        }

        Log::info('Found businesses needing data enhancement', [
            'scraping_job_id' => $scrapingJob->id,
            'incomplete_businesses_count' => $incompleteBusinesses->count(),
            'total_businesses' => $businesses->count(),
        ]);

        $enhancedCount = 0;

        foreach ($incompleteBusinesses as $business) {
            $missingFields = $this->completenessService->getMissingCriticalFields($business);

            if (!empty($missingFields)) {
                Log::info('Dispatching enhancement job for business', [
                    'business_id' => $business->id,
                    'business_name' => $business->name,
                    'missing_fields' => $missingFields,
                ]);

                // Dispatch enhancement job for this business
                EnhanceBusinessDataJob::dispatch($business, $missingFields, $scrapingJob);
                $enhancedCount++;
            }
        }

        if ($enhancedCount > 0) {
            $scrapingJob->update([
                'discovered_businesses_count' => $enhancedCount,
                'status' => \App\Enums\ScrapingJobStatusEnum::COMPLETED,
                'message' => "Dispatched enhancement jobs for {$enhancedCount} businesses with incomplete data."
            ]);
        } else {
            // If no businesses needed enhancement, try scraping new ones
            Log::info('No businesses needed enhancement, falling back to new business scraping', [
                'scraping_job_id' => $scrapingJob->id,
            ]);

            $this->performRealScraping($scrapingJob);
        }
    }

    /**
     * Enhance specific fields for a business
     *
     * @param Business $business
     * @param array $fieldsToEnhance
     * @return array Enhanced data
     */
    public function enhanceBusinessFields(Business $business, array $fieldsToEnhance): array
    {
        $enhancedData = [];

        Log::info('Enhancing specific fields for business', [
            'business_id' => $business->id,
            'fields_to_enhance' => $fieldsToEnhance,
        ]);

        // Use real data enhancement instead of generating fake data
        foreach ($fieldsToEnhance as $field) {
            switch ($field) {
                case 'email':
                    if (empty($business->email)) {
                        $email = $this->findRealBusinessEmail($business);
                        if ($email) {
                            $enhancedData['email'] = $email;
                        }
                    }
                    break;

                case 'lat':
                case 'lng':
                    if (empty($business->lat) || empty($business->lng)) {
                        $coordinates = $this->geocodeBusinessAddress($business);
                        if ($coordinates) {
                            $enhancedData['lat'] = $coordinates['lat'];
                            $enhancedData['lng'] = $coordinates['lng'];
                        }
                    }
                    break;

                case 'address':
                    if (empty($business->address) || !preg_match('/\b\d{5}\b/', $business->address)) {
                        $address = $this->findRealBusinessAddress($business);
                        if ($address) {
                            $enhancedData['address'] = $address;
                        }
                    }
                    break;

                case 'phone':
                    if (empty($business->phone)) {
                        $phone = $this->findRealBusinessPhone($business);
                        if ($phone) {
                            $enhancedData['phone'] = $phone;
                        }
                    }
                    break;

                case 'website':
                    if (empty($business->website)) {
                        $website = $this->findRealBusinessWebsite($business);
                        if ($website) {
                            $enhancedData['website'] = $website;
                        }
                    }
                    break;
            }
        }

        return $enhancedData;
    }

    /**
     * Find real business email using web scraping or APIs
     *
     * @param Business $business
     * @return string|null
     */
    protected function findRealBusinessEmail(Business $business): ?string
    {
        Log::info('Attempting to find real email for business', [
            'business_id' => $business->id,
            'business_name' => $business->name
        ]);

        // Try to scrape email from business website if available
        if (!empty($business->website)) {
            $email = $this->scrapeEmailFromWebsite($business->website);
            if ($email) {
                Log::info('Found email from business website', [
                    'business_id' => $business->id,
                    'email' => $email
                ]);
                return $email;
            }
        }

        // Try to use Google Places API or similar service
        $email = $this->findEmailViaGooglePlaces($business);
        if ($email) {
            Log::info('Found email via Google Places', [
                'business_id' => $business->id,
                'email' => $email
            ]);
            return $email;
        }

        Log::info('Could not find real email for business', [
            'business_id' => $business->id,
            'business_name' => $business->name
        ]);

        return null;
    }

    /**
     * Geocode business address to get real coordinates
     *
     * @param Business $business
     * @return array|null
     */
    protected function geocodeBusinessAddress(Business $business): ?array
    {
        $address = $business->address ?? $business->location ?? '';

        if (empty($address)) {
            Log::info('No address available for geocoding', [
                'business_id' => $business->id,
                'business_name' => $business->name
            ]);
            return null;
        }

        Log::info('Attempting to geocode business address', [
            'business_id' => $business->id,
            'address' => $address
        ]);

        // Try to use a real geocoding service
        $coordinates = $this->geocodeAddress($address);

        if ($coordinates) {
            Log::info('Successfully geocoded business address', [
                'business_id' => $business->id,
                'address' => $address,
                'coordinates' => $coordinates
            ]);
            return $coordinates;
        }

        Log::info('Could not geocode business address', [
            'business_id' => $business->id,
            'address' => $address
        ]);

        return null;
    }

    /**
     * Find real business address using web scraping or APIs
     *
     * @param Business $business
     * @return string|null
     */
    protected function findRealBusinessAddress(Business $business): ?string
    {
        Log::info('Attempting to find real address for business', [
            'business_id' => $business->id,
            'business_name' => $business->name
        ]);

        // Try to get address from Google Places API or similar service
        $address = $this->findAddressViaGooglePlaces($business);
        if ($address) {
            Log::info('Found address via Google Places', [
                'business_id' => $business->id,
                'address' => $address
            ]);
            return $address;
        }

        Log::info('Could not find real address for business', [
            'business_id' => $business->id,
            'business_name' => $business->name
        ]);

        return null;
    }

    /**
     * Find real business phone number using web scraping or APIs
     *
     * @param Business $business
     * @return string|null
     */
    protected function findRealBusinessPhone(Business $business): ?string
    {
        Log::info('Attempting to find real phone for business', [
            'business_id' => $business->id,
            'business_name' => $business->name
        ]);

        // Try to get phone from Google Places API or similar service
        $phone = $this->findPhoneViaGooglePlaces($business);
        if ($phone) {
            Log::info('Found phone via Google Places', [
                'business_id' => $business->id,
                'phone' => $phone
            ]);
            return $phone;
        }

        Log::info('Could not find real phone for business', [
            'business_id' => $business->id,
            'business_name' => $business->name
        ]);

        return null;
    }

    /**
     * Find real business website using web scraping or APIs
     *
     * @param Business $business
     * @return string|null
     */
    protected function findRealBusinessWebsite(Business $business): ?string
    {
        Log::info('Attempting to find real website for business', [
            'business_id' => $business->id,
            'business_name' => $business->name
        ]);

        // Try to get website from Google Places API or similar service
        $website = $this->findWebsiteViaGooglePlaces($business);
        if ($website) {
            Log::info('Found website via Google Places', [
                'business_id' => $business->id,
                'website' => $website
            ]);
            return $website;
        }

        Log::info('Could not find real website for business', [
            'business_id' => $business->id,
            'business_name' => $business->name
        ]);

        return null;
    }

    /**
     * Call the comprehensive Node.js scraper to get real business data
     * Uses the full index.js scraper with detailed business information extraction
     *
     * @param string $location
     * @param string $category
     * @return array
     */
    protected function callNodeJsScraper(string $location, string $category): array
    {
        $nodeScriptPath = base_path('index.js');

        if (!file_exists($nodeScriptPath)) {
            throw new \Exception("Node.js scraper script not found at: {$nodeScriptPath}");
        }

        // Create a unique output file for this scraping job
        $outputFile = base_path('business_data_' . uniqid() . '.json');
        $tempFile = base_path('temp_business_data_' . uniqid() . '.json');

        try {
            // Execute the comprehensive Node.js scraper with specific parameters
            // Use the full index.js scraper which has detailed business data extraction
            $command = sprintf(
                'cd %s && node index.js --locations "%s" --categories "%s" --max %d --no-resume 2>&1',
                escapeshellarg(base_path()),
                escapeshellarg($location),
                escapeshellarg($category),
                20
            );

            Log::info('Executing comprehensive Node.js scraper', [
                'command' => $command,
                'location' => $location,
                'category' => $category,
                'expected_output_file' => base_path('business_data.json')
            ]);

            $output = [];
            $returnCode = 0;

            // Set a longer timeout for comprehensive scraping
            $descriptorspec = [
                0 => ["pipe", "r"],
                1 => ["pipe", "w"],
                2 => ["pipe", "w"]
            ];

            $process = proc_open($command, $descriptorspec, $pipes, base_path());

            if (is_resource($process)) {
                fclose($pipes[0]);

                $stdout = stream_get_contents($pipes[1]);
                $stderr = stream_get_contents($pipes[2]);

                fclose($pipes[1]);
                fclose($pipes[2]);

                $returnCode = proc_close($process);

                $output = array_merge(
                    explode("\n", $stdout),
                    explode("\n", $stderr)
                );
            }

            Log::info('Node.js scraper execution completed', [
                'return_code' => $returnCode,
                'output_lines' => count($output),
                'location' => $location,
                'category' => $category
            ]);

            if ($returnCode !== 0) {
                Log::error('Node.js scraper failed', [
                    'return_code' => $returnCode,
                    'output' => implode("\n", array_slice($output, -20)), // Last 20 lines
                    'command' => $command
                ]);
                throw new \Exception("Node.js scraper failed with return code {$returnCode}");
            }

            // Read the scraped data from the default output file
            $defaultOutputFile = base_path('business_data.json');
            if (!file_exists($defaultOutputFile)) {
                Log::warning('Default scraper output file not found', [
                    'expected_file' => $defaultOutputFile,
                    'location' => $location,
                    'category' => $category
                ]);
                return [];
            }

            $scrapedData = json_decode(file_get_contents($defaultOutputFile), true);

            if (!is_array($scrapedData)) {
                Log::warning('Invalid scraped data format', [
                    'output_file' => $defaultOutputFile,
                    'location' => $location,
                    'category' => $category
                ]);
                return [];
            }

            // Filter data for the specific location and category we requested
            $filteredData = array_filter($scrapedData, function($business) use ($location, $category) {
                return (
                    !empty($business['name']) &&
                    (!isset($business['isSample']) || !$business['isSample']) &&
                    (!isset($business['errorRecovery']) || !$business['errorRecovery']) &&
                    (empty($business['location']) || stripos($business['location'], $location) !== false) &&
                    (empty($business['category']) || stripos($business['category'], $category) !== false)
                );
            });

            Log::info('Successfully retrieved and filtered scraped data', [
                'total_scraped' => count($scrapedData),
                'filtered_businesses' => count($filteredData),
                'location' => $location,
                'category' => $category,
                'output_file' => $defaultOutputFile
            ]);

            return array_values($filteredData);

        } finally {
            // Clean up temporary files
            $filesToClean = [
                $outputFile,
                $tempFile,
                base_path('business_data.json'),
                base_path('temp_business_data.json')
            ];

            foreach ($filesToClean as $file) {
                if (file_exists($file)) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * Validate and clean comprehensive scraped business data from JavaScript scraper
     *
     * @param array $businessData
     * @param string $category
     * @param string $location
     * @return array|null
     */
    protected function validateAndCleanScrapedData(array $businessData, string $category, string $location): ?array
    {
        // Required fields validation
        if (empty($businessData['name'])) {
            Log::warning('Skipping business with missing name', ['business_data' => $businessData]);
            return null;
        }

        // Skip sample/test data
        if (isset($businessData['isSample']) && $businessData['isSample']) {
            Log::info('Skipping sample business data', ['business_name' => $businessData['name']]);
            return null;
        }

        if (isset($businessData['errorRecovery']) && $businessData['errorRecovery']) {
            Log::info('Skipping error recovery business data', ['business_name' => $businessData['name']]);
            return null;
        }

        // Clean and validate the data with comprehensive field mapping
        $cleanedData = [
            'name' => trim($businessData['name']),
            'category' => $category,
            'location' => $location,
        ];

        // Address handling - comprehensive extraction
        if (!empty($businessData['address'])) {
            $address = trim($businessData['address']);
            // Clean up address formatting from Google Maps
            $address = preg_replace('/\s+/', ' ', $address);
            $cleanedData['address'] = $address;
            // Note: zip_code is automatically generated from location and address fields
        }

        // Phone number handling with comprehensive validation
        if (!empty($businessData['phone'])) {
            $phone = $this->cleanPhoneNumber($businessData['phone']);
            if ($phone && !$this->isFakePhoneNumber($phone)) {
                $cleanedData['phone'] = $phone;
            }
        }

        // Website handling with comprehensive validation
        if (!empty($businessData['website'])) {
            $website = $this->cleanWebsiteUrl($businessData['website']);
            if ($website && !$this->isFakeWebsite($website)) {
                $cleanedData['website'] = $website;
            }
        }

        // Email handling with comprehensive validation
        if (!empty($businessData['email'])) {
            $email = trim($businessData['email']);
            if (filter_var($email, FILTER_VALIDATE_EMAIL) && !$this->isFakeEmail($email)) {
                $cleanedData['email'] = $email;
            }
        }

        // Coordinates handling - validate real coordinates
        if (!empty($businessData['lat']) && !empty($businessData['lng'])) {
            $lat = floatval($businessData['lat']);
            $lng = floatval($businessData['lng']);

            if ($lat >= -90 && $lat <= 90 && $lng >= -180 && $lng <= 180 &&
                !($lat == 40.7128 && $lng == -74.0060)) { // Skip default NYC coordinates
                $cleanedData['lat'] = $lat;
                $cleanedData['lng'] = $lng;
            }
        }

        // Business hours handling - comprehensive format support
        if (!empty($businessData['hours'])) {
            if (is_array($businessData['hours'])) {
                $cleanedData['hours'] = $this->normalizeBusinessHours($businessData['hours']);
            } elseif (is_string($businessData['hours'])) {
                $cleanedData['hours'] = $this->parseBusinessHoursString($businessData['hours']);
            }
        }

        // Photos handling - validate URLs and filter placeholders
        if (!empty($businessData['photos']) && is_array($businessData['photos'])) {
            $validPhotos = array_filter($businessData['photos'], function($photo) {
                return !empty($photo) &&
                       filter_var($photo, FILTER_VALIDATE_URL) &&
                       !stripos($photo, 'placeholder') &&
                       !stripos($photo, 'via.placeholder');
            });
            if (!empty($validPhotos)) {
                $cleanedData['photos'] = array_values($validPhotos);
            }
        }

        // Services handling - clean and validate
        if (!empty($businessData['services']) && is_array($businessData['services'])) {
            $validServices = array_filter($businessData['services'], function($service) {
                return !empty(trim($service)) &&
                       strlen(trim($service)) > 3 &&
                       !stripos($service, 'professional service') &&
                       !stripos($service, 'quality work');
            });
            if (!empty($validServices)) {
                $cleanedData['services'] = array_values($validServices);
            }
        }

        // Reviews handling - comprehensive validation
        if (!empty($businessData['reviews']) && is_array($businessData['reviews'])) {
            $validReviews = array_filter($businessData['reviews'], function($review) {
                return is_array($review) &&
                       !empty($review['text']) &&
                       strlen($review['text']) > 10 &&
                       !stripos($review['text'], 'great service from scraped business');
            });
            if (!empty($validReviews)) {
                $cleanedData['reviews'] = array_values($validReviews);
            }
        }

        // Rating handling - validate realistic ratings
        if (!empty($businessData['rating'])) {
            $rating = floatval($businessData['rating']);
            if ($rating >= 0 && $rating <= 5 && $rating != 5.0) { // Skip perfect 5.0 ratings as potentially fake
                $cleanedData['rating'] = $rating;
            }
        }

        // Final validation - ensure we have meaningful data
        $hasContactInfo = !empty($cleanedData['address']) || !empty($cleanedData['phone']) ||
                         !empty($cleanedData['website']) || !empty($cleanedData['email']);

        if (!$hasContactInfo) {
            Log::info('Skipping business without contact information', [
                'business_name' => $cleanedData['name'],
                'category' => $category,
                'location' => $location
            ]);
            return null;
        }

        return $cleanedData;
    }

    /**
     * Clean and validate phone number with comprehensive fake detection
     *
     * @param string $phone
     * @return string|null
     */
    protected function cleanPhoneNumber(string $phone): ?string
    {
        // Remove all non-digit characters except + and ()
        $cleaned = preg_replace('/[^\d\+\(\)\-\s]/', '', trim($phone));

        // Basic validation - should have at least 10 digits
        $digits = preg_replace('/[^\d]/', '', $cleaned);

        if (strlen($digits) >= 10) {
            return $cleaned;
        }

        return null;
    }

    /**
     * Check if phone number is fake/placeholder
     *
     * @param string $phone
     * @return bool
     */
    protected function isFakePhoneNumber(string $phone): bool
    {
        $fakePatterns = [
            '555-000',
            '555-0000',
            '123-456',
            '000-000',
            '111-111',
            '222-222',
            '333-333',
            '444-444',
            '666-666',
            '777-777',
            '888-888',
            '999-999'
        ];

        foreach ($fakePatterns as $pattern) {
            if (stripos($phone, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Clean and validate website URL with comprehensive fake detection
     *
     * @param string $website
     * @return string|null
     */
    protected function cleanWebsiteUrl(string $website): ?string
    {
        $website = trim($website);

        // Add protocol if missing
        if (!preg_match('/^https?:\/\//', $website)) {
            $website = 'https://' . $website;
        }

        if (filter_var($website, FILTER_VALIDATE_URL)) {
            return $website;
        }

        return null;
    }

    /**
     * Check if website is fake/placeholder
     *
     * @param string $website
     * @return bool
     */
    protected function isFakeWebsite(string $website): bool
    {
        $fakeDomains = [
            'sample-business.com',
            'placeholder.com',
            'example.com',
            'test.com',
            'scraped-business',
            'business.com'
        ];

        foreach ($fakeDomains as $domain) {
            if (stripos($website, $domain) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if email is fake/placeholder
     *
     * @param string $email
     * @return bool
     */
    protected function isFakeEmail(string $email): bool
    {
        $fakeDomains = [
            '@scraped.com',
            '@sample.com',
            '@test.com',
            '@example.com',
            '@placeholder.com'
        ];

        foreach ($fakeDomains as $domain) {
            if (stripos($email, $domain) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Normalize business hours from various formats
     *
     * @param array $hours
     * @return array
     */
    protected function normalizeBusinessHours(array $hours): array
    {
        $normalized = [];

        foreach ($hours as $day => $time) {
            if (is_array($time) && isset($time['open'], $time['close'])) {
                $normalized[$day] = $time;
            } elseif (is_string($time)) {
                $normalized[$day] = $time;
            }
        }

        return $normalized;
    }

    /**
     * Parse business hours from string format
     *
     * @param string $hoursString
     * @return array
     */
    protected function parseBusinessHoursString(string $hoursString): array
    {
        // Simple parsing for common formats like "Mon-Fri: 9am-5pm"
        $hours = [];

        if (preg_match('/(\w+)-(\w+):\s*(\d+\w+)-(\d+\w+)/', $hoursString, $matches)) {
            $startDay = strtolower($matches[1]);
            $endDay = strtolower($matches[2]);
            $openTime = $matches[3];
            $closeTime = $matches[4];

            $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
            $startIndex = array_search($startDay, $days);
            $endIndex = array_search($endDay, $days);

            if ($startIndex !== false && $endIndex !== false) {
                for ($i = $startIndex; $i <= $endIndex; $i++) {
                    $hours[$days[$i]] = ['open' => $openTime, 'close' => $closeTime];
                }
            }
        }

        return $hours;
    }

    /**
     * Find existing business to avoid duplicates
     *
     * @param array $businessData
     * @return Business|null
     */
    protected function findExistingBusiness(array $businessData): ?Business
    {
        $query = Business::where('name', $businessData['name']);

        if (!empty($businessData['address'])) {
            $query->where('address', $businessData['address']);
        }

        if (!empty($businessData['phone'])) {
            $query->orWhere('phone', $businessData['phone']);
        }

        return $query->first();
    }

    /**
     * Process the job notification campaign after scraping is complete.
     * Implements continuous scraping logic until businesses are found.
     *
     * @param ScrapingJob $scrapingJob
     */
    protected function processCampaignAfterScraping(ScrapingJob $scrapingJob): void
    {
        $campaign = $scrapingJob->jobNotificationCampaign;

        if ($scrapingJob->discovered_businesses_count > 0) {
            Log::info('Re-processing job notification campaign after successful scraping.', [
                'campaign_id' => $campaign->id,
                'discovered_businesses_count' => $scrapingJob->discovered_businesses_count
            ]);

            // Use the JobNotificationService to re-run business discovery
            // This will find the newly scraped businesses and set up the campaign
            $businessesFound = $this->jobNotificationService->processBusinessDiscovery(
                $campaign,
                $campaign->job_zip_code,
                $campaign->job_category,
                $campaign->search_radius
            );

            if ($businessesFound) {
                Log::info('Campaign successfully re-processed after scraping', [
                    'campaign_id' => $campaign->id,
                    'business_count' => $campaign->business_count
                ]);
            } else {
                // Continue scraping in nearby areas instead of rejecting
                $this->triggerContinuousScraping($campaign, $scrapingJob);
            }
        } else {
            // No businesses found in this scraping attempt, continue scraping
            Log::info('No businesses found in current scraping attempt, continuing to search nearby areas.', [
                'scraping_job_id' => $scrapingJob->id,
                'campaign_id' => $campaign->id,
            ]);

            $this->triggerContinuousScraping($campaign, $scrapingJob);
        }
    }

    /**
     * Trigger continuous scraping in nearby areas until businesses are found.
     * Based on the JavaScript reference implementation logic.
     *
     * @param \App\Models\JobNotificationCampaign $campaign
     * @param ScrapingJob $previousScrapingJob
     */
    public function triggerContinuousScraping($campaign, $previousScrapingJob): void
    {
        $currentRadius = $campaign->search_radius ?? config('job_notification.default_radius', 30);
        $maxRadius = config('job_notification.max_radius', 75);

        // Check if we've reached the maximum search radius
        if ($currentRadius >= $maxRadius) {
            Log::warning('Maximum search radius reached, rejecting campaign', [
                'campaign_id' => $campaign->id,
                'current_radius' => $currentRadius,
                'max_radius' => $maxRadius,
                'target_zip_code' => $campaign->job_zip_code
            ]);

            $campaign->update([
                'status' => \App\Enums\JobNotificationStatusEnum::REJECTED,
                'rejection_reason' => "No businesses found within maximum search radius of {$maxRadius} miles from zip code {$campaign->job_zip_code}"
            ]);
            return;
        }

        // Expand search radius for next attempt
        $newRadius = min($currentRadius + 15, $maxRadius);

        Log::info('Expanding search radius for continuous scraping', [
            'campaign_id' => $campaign->id,
            'current_radius' => $currentRadius,
            'new_radius' => $newRadius,
            'target_zip_code' => $campaign->job_zip_code
        ]);

        // Update campaign search radius
        $campaign->search_radius = $newRadius;
        $campaign->save();

        // Create a new scraping job for the expanded area
        $newScrapingJob = \App\Models\ScrapingJob::create([
            'job_notification_campaign_id' => $campaign->id,
            'location_query' => $campaign->job_zip_code,
            'category_query' => $campaign->job_category ?? 'general',
            'status' => \App\Enums\ScrapingJobStatusEnum::PENDING,
        ]);

        // Dispatch the new scraping job
        \App\Jobs\ScrapeBusinessesJob::dispatch($newScrapingJob);

        Log::info('Dispatched continuous scraping job with expanded radius', [
            'campaign_id' => $campaign->id,
            'new_scraping_job_id' => $newScrapingJob->id,
            'expanded_radius' => $newRadius,
            'previous_scraping_job_id' => $previousScrapingJob->id
        ]);
    }

    /**
     * Scrape email from business website
     *
     * @param string $website
     * @return string|null
     */
    protected function scrapeEmailFromWebsite(string $website): ?string
    {
        try {
            // Simple email scraping from website
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Mozilla/5.0 (compatible; BusinessScraper/1.0)'
                ]
            ]);

            $content = @file_get_contents($website, false, $context);
            if ($content === false) {
                return null;
            }

            // Look for email patterns
            $pattern = '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/';
            if (preg_match($pattern, $content, $matches)) {
                $email = $matches[0];

                // Filter out common non-business emails
                $excludePatterns = [
                    'noreply@',
                    'no-reply@',
                    'support@google',
                    'admin@',
                    'webmaster@',
                    'info@example',
                    'test@'
                ];

                foreach ($excludePatterns as $exclude) {
                    if (stripos($email, $exclude) !== false) {
                        return null;
                    }
                }

                return $email;
            }
        } catch (\Exception $e) {
            Log::warning('Error scraping email from website', [
                'website' => $website,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Geocode address using a real geocoding service
     *
     * @param string $address
     * @return array|null
     */
    protected function geocodeAddress(string $address): ?array
    {
        try {
            // Use a free geocoding service like Nominatim (OpenStreetMap)
            $encodedAddress = urlencode($address);
            $url = "https://nominatim.openstreetmap.org/search?format=json&q={$encodedAddress}&limit=1";

            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'BusinessScraper/1.0 (<EMAIL>)'
                ]
            ]);

            $response = @file_get_contents($url, false, $context);
            if ($response === false) {
                return null;
            }

            $data = json_decode($response, true);
            if (!empty($data) && isset($data[0]['lat'], $data[0]['lon'])) {
                return [
                    'lat' => floatval($data[0]['lat']),
                    'lng' => floatval($data[0]['lon'])
                ];
            }
        } catch (\Exception $e) {
            Log::warning('Error geocoding address', [
                'address' => $address,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Placeholder for Google Places API integration
     * In a real implementation, this would use Google Places API
     *
     * @param Business $business
     * @return string|null
     */
    protected function findEmailViaGooglePlaces(Business $business): ?string
    {
        // TODO: Implement Google Places API integration
        // This would require a Google Places API key and proper implementation
        Log::info('Google Places API integration not implemented for email lookup', [
            'business_id' => $business->id
        ]);
        return null;
    }

    /**
     * Placeholder for Google Places API integration for address
     *
     * @param Business $business
     * @return string|null
     */
    protected function findAddressViaGooglePlaces(Business $business): ?string
    {
        // TODO: Implement Google Places API integration
        Log::info('Google Places API integration not implemented for address lookup', [
            'business_id' => $business->id
        ]);
        return null;
    }

    /**
     * Placeholder for Google Places API integration for phone
     *
     * @param Business $business
     * @return string|null
     */
    protected function findPhoneViaGooglePlaces(Business $business): ?string
    {
        // TODO: Implement Google Places API integration
        Log::info('Google Places API integration not implemented for phone lookup', [
            'business_id' => $business->id
        ]);
        return null;
    }

    /**
     * Placeholder for Google Places API integration for website
     *
     * @param Business $business
     * @return string|null
     */
    protected function findWebsiteViaGooglePlaces(Business $business): ?string
    {
        // TODO: Implement Google Places API integration
        Log::info('Google Places API integration not implemented for website lookup', [
            'business_id' => $business->id
        ]);
        return null;
    }
}