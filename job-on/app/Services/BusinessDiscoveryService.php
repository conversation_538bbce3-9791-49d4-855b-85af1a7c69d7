<?php

namespace App\Services;

use App\Models\Business;
use App\Models\ZipCode as ZipCodeModel;
use App\Services\ZipCode\ZipCode as ZipCodeService;
use App\Services\BusinessDataCompletenessService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class BusinessDiscoveryService
{
    /**
     * The ZipCode service instance
     *
     * @var ZipCodeService
     */
    protected $zipCodeService;

    /**
     * The BusinessDataCompletenessService instance
     *
     * @var BusinessDataCompletenessService
     */
    protected $completenessService;

    /**
     * Default radius for business search (in miles)
     *
     * @var int
     */
    protected $defaultRadius;

    /**
     * Maximum radius for business search (in miles)
     *
     * @var int
     */
    protected $maxRadius;

    /**
     * Create a new BusinessDiscoveryService instance.
     *
     * @param ZipCodeService $zipCodeService
     * @param BusinessDataCompletenessService $completenessService
     */
    public function __construct(ZipCodeService $zipCodeService, BusinessDataCompletenessService $completenessService)
    {
        $this->zipCodeService = $zipCodeService;
        $this->completenessService = $completenessService;
        $this->defaultRadius = config('job_notification.default_radius', 30);
        $this->maxRadius = config('job_notification.max_radius', 75);
    }

    /**
     * Find businesses within radius of a zip code and filter by category
     *
     * @param string $zipCode The zip code to search around
     * @param string|array|null $category The category or categories to filter by
     * @param float|null $radius The radius in miles to search within (defaults to app config value)
     * @param bool $requireCompleteData Whether to only return businesses with complete data
     * @return Collection A collection of Business models sorted by distance
     */
    public function findBusinesses(string $zipCode, $category = null, ?float $radius = null, bool $requireCompleteData = true): Collection
    {
        // Use provided radius, default to config value
        $radius = $radius ?? $this->defaultRadius;

        // Cap radius at maximum allowed value
        $radius = min($radius, $this->maxRadius);

        try {
            // Start building query to find businesses
            $query = Business::query();

            // Only require email if we need complete data
            if ($requireCompleteData) {
                $query->whereNotNull('email'); // Ensure businesses have email for notifications
            }

            // First try exact zip code match (highest priority)
            $exactMatches = (clone $query)->where('zip_code', $zipCode)->get();

            if ($exactMatches->isNotEmpty()) {
                Log::info("Found businesses with exact zip code match", [
                    'zip_code' => $zipCode,
                    'exact_matches' => $exactMatches->count()
                ]);
                $businesses = $exactMatches;
            } else {
                // If no exact matches, try radius search
                $zipCodes = $this->zipCodeService->getZipCodesInRadiusRaw($zipCode, $radius);

                if (empty($zipCodes)) {
                    Log::warning("No zip codes found within {$radius} miles of {$zipCode}");
                    $businesses = collect(); // Return empty collection
                } else {
                    // Use the zip codes found in radius
                    $query->whereIn('zip_code', $zipCodes);
                    $businesses = $query->get();

                    Log::info("Found businesses within radius", [
                        'zip_code' => $zipCode,
                        'radius' => $radius,
                        'zip_codes_in_radius' => count($zipCodes),
                        'businesses_found' => $businesses->count()
                    ]);
                }
            }

            // Filter by category if provided
            if (!is_null($category) && $businesses->isNotEmpty()) {
                $originalCount = $businesses->count();
                if (is_array($category)) {
                    $businesses = $businesses->whereIn('category', $category);
                } else {
                    $businesses = $businesses->where('category', $category);
                }

                Log::info("Applied category filter", [
                    'category' => $category,
                    'original_count' => $originalCount,
                    'filtered_count' => $businesses->count()
                ]);
            }

            // Filter by data completeness if required
            if ($requireCompleteData) {
                $originalCount = $businesses->count();
                $businesses = $this->completenessService->filterCompleteBusinesses($businesses);

                if ($businesses->count() < $originalCount) {
                    Log::info('Filtered businesses by data completeness', [
                        'zip_code' => $zipCode,
                        'original_count' => $originalCount,
                        'complete_count' => $businesses->count(),
                        'filtered_out' => $originalCount - $businesses->count(),
                    ]);
                }
            }
            
            // If we found businesses, sort them by distance
            if ($businesses->isNotEmpty()) {
                // Get origin zip code location
                $originZipCode = ZipCodeModel::where('zip_code', $zipCode)->first();
                
                if ($originZipCode && $originZipCode->lat && $originZipCode->lng) {
                    $originLat = (float)$originZipCode->lat;
                    $originLng = (float)$originZipCode->lng;
                    
                    // Calculate distance for each business and sort
                    $businesses = $businesses->map(function ($business) use ($originLat, $originLng) {
                        // Skip distance calculation if missing coordinates
                        if (empty($business->lat) || empty($business->lng)) {
                            $business->distance = null;
                            return $business;
                        }
                        
                        // Calculate distance using Haversine formula
                        $businessLat = (float)$business->lat;
                        $businessLng = (float)$business->lng;
                        
                        $distance = $this->calculateDistance($originLat, $originLng, $businessLat, $businessLng);
                        $business->distance = $distance;
                        
                        return $business;
                    })
                    ->filter(function ($business) {
                        // Remove businesses without coordinates
                        return !is_null($business->distance);
                    })
                    ->sortBy('distance');
                }
            }
            
            Log::info('Found businesses for notification', [
                'zip_code' => $zipCode,
                'radius' => $radius,
                'business_count' => $businesses->count(),
                'category' => $category,
            ]);
            
            return $businesses;
            
        } catch (\Exception $e) {
            Log::error('Error finding businesses: ' . $e->getMessage());
            return new Collection();
        }
    }
    
    /**
     * Calculate distance between two points using Haversine formula
     *
     * @param float $lat1 Origin latitude
     * @param float $lng1 Origin longitude
     * @param float $lat2 Destination latitude
     * @param float $lng2 Destination longitude
     * @return float Distance in miles
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        // Earth's radius in miles
        $earthRadius = 3959;
        
        // Convert degrees to radians
        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);
        
        // Calculate differences
        $latDiff = $lat2 - $lat1;
        $lngDiff = $lng2 - $lng1;
        
        // Haversine formula
        $a = sin($latDiff / 2) * sin($latDiff / 2) +
            cos($lat1) * cos($lat2) * sin($lngDiff / 2) * sin($lngDiff / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earthRadius * $c;
        
        return $distance;
    }

    /**
     * Find businesses with complete data for notifications
     *
     * @param string $zipCode
     * @param string|array|null $category
     * @param float|null $radius
     * @return Collection
     */
    public function findCompleteBusinesses(string $zipCode, $category = null, ?float $radius = null): Collection
    {
        return $this->findBusinesses($zipCode, $category, $radius, true);
    }

    /**
     * Find all businesses (including incomplete ones) in the area
     *
     * @param string $zipCode
     * @param string|array|null $category
     * @param float|null $radius
     * @return Collection
     */
    public function findAllBusinesses(string $zipCode, $category = null, ?float $radius = null): Collection
    {
        return $this->findBusinesses($zipCode, $category, $radius, false);
    }

    /**
     * Find businesses that need data enhancement
     *
     * @param string $zipCode
     * @param string|array|null $category
     * @param float|null $radius
     * @return Collection
     */
    public function findBusinessesNeedingEnhancement(string $zipCode, $category = null, ?float $radius = null): Collection
    {
        $allBusinesses = $this->findAllBusinesses($zipCode, $category, $radius);
        return $this->completenessService->filterIncompleteBusinesses($allBusinesses);
    }


}